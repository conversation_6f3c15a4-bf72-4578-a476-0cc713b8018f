﻿@using HalalaPlusProject.Areas.Identity.Data
@using Microsoft.AspNetCore.Identity
@inject SignInManager<HalalaPlusProjectUser> SignInManager
@inject UserManager<HalalaPlusProjectUser> UserManager
@using System.Globalization
@using Microsoft.AspNetCore.Mvc.Localization

@inject IViewLocalizer localizer

@{
    var isRTL = CultureInfo.CurrentCulture.Name.StartsWith("ar");
}
<!DOCTYPE html>
<html lang="en" dir="@(isRTL ? "RTL" : "LTR")" style="overflow-x: hidden;">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - HalalaPlus</title>
    @*<link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />*@
    @*<link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />*@
    @*<link rel="stylesheet" href="~/HalalaPlusProjectstyles.css" asp-append-version="true" />*@
    <!--     Fonts and icons     -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />
    <!-- Nucleo Icons -->
    <link href="~/assets/css/nucleo-icons.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/selectize.js/0.12.6/css/selectize.bootstrap3.min.css" integrity="sha256-ze/OEYGcFbPRmvCnrSeKbRTtjG4vGLHXgOqsyLFTRjg=" crossorigin="anonymous" />



    <link href="~/css/jquery-ui.css" rel="stylesheet" />
    <!-- Font Awesome Icons -->
    <link href="~/assets/css/nucleo-svg.css" rel="stylesheet" />
    <!-- CSS Files -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">

    @if (isRTL)
    {
        <link id="pagestyle" href="~/assets/css/soft-ui-dashboard.css?v=1.0.3" rel="stylesheet" asp-append-version="true" />
    }
    else
    {
        <link id="pagestyle" href="~/assets/css/soft-ui-dashboardltr.css?v=1.0.3" rel="stylesheet" asp-append-version="true" />
    }
    <link href="~/css/bootstrap-icons.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
    <link href="~/css/site.css" rel="stylesheet" />
    <link href="~/css/dataTables.min.css" rel="stylesheet" />
    <link href="~/css/sweetalert2.css" rel="stylesheet">

    <!-- Template CSS Files -->
    <link href="~/css/template-variables.css" rel="stylesheet" />
    <link href="~/css/template-main.css" rel="stylesheet" />
    <link href="~/css/template-sidebar.css" rel="stylesheet" />
    <link href="~/css/template-navbar.css" rel="stylesheet" />

    <script src="~/js/sweetalert2.js"></script>
    <style>
        /*image logo start*/

        .mainInfo {
            background-color: #fff;
            border: .01rem round #e0e1ed;
            border-radius: 20px;
            color: #585CA1;
            width: 100%;
            height: 5em;
            box-shadow: 0px 0px 17px -5px rgba(0,0,0,0.75);
            margin-top: 3em;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .circle {
            position: relative;
            width: 8rem;
            height: 8rem;
            background: #fff;
            border-radius: 10%;
            box-shadow: 0px 0px 11px -5px rgba(0,0,0,0.65);
        }

            /*   .circle:before {
                        position: absolute;
                        content: "";
                        width: 15em;
                        height: 5em;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: #fff;
                    } */

            .circle img {
                position: absolute;
                max-width: 85%;
                border-radius: 10%;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 200;
            }

        .btnicons {
            width: auto !important;
            padding-right: 15px !important;
            padding-left: 15px !important;
        }
        .navbar-vertical .navbar-nav .nav-link > i{
            min-width: 1.3rem;
        }

        .navbar-vertical.navbar-expand-xs .navbar-nav .nav-link{
            font-size  : 18px;
        }

        .navbar-vertical .navbar-nav .nav-link{
            padding-left: 5px;
            padding-right: 5px;
        }

        /* Sidebar Mobile Improvements */
        .mobile-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 989;
            display: none;
            transition: opacity 0.3s ease;
        }

        @* @media (max-width: 1199.98px) {
            .sidenav {
                transition: transform 0.3s ease-in-out;
            }

            .g-sidenav-show:not(.g-sidenav-pinned) .sidenav {
                transform: translateX(-17.125rem);
            }

            .g-sidenav-show.g-sidenav-pinned .sidenav {
                transform: translateX(0);
                box-shadow: 0 20px 27px 0 rgba(0, 0, 0, 0.2);
            }

            .rtl .g-sidenav-show:not(.g-sidenav-pinned) .sidenav {
                transform: translateX(17.125rem);
            }

            .rtl .g-sidenav-show.g-sidenav-pinned .sidenav {
                transform: translateX(0);
            }
        } *@

        /* Sidebar Toggle Button Improvements */
        #iconNavbarSidenav {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        #iconNavbarSidenav:hover {
            opacity: 0.8;
        }

        .sidenav-toggler-inner {
            width: 20px;
            height: 15px;
            position: relative;
        }

        .sidenav-toggler-line {
            display: block;
            height: 2px;
            width: 100%;
            background: currentColor;
            transition: all 0.3s ease;
        }

        .sidenav-toggler-line:not(:last-child) {
            margin-bottom: 3px;
        }

        /*image logo end*/
    </style>

    @*     <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" /> *@
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    @*  i can put it in the body later   <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"> *@
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">

</head>
<body class="@(isRTL ? "rtl" : "ltr")">
    <!-- Wrapper -->
    <div class="wrapper">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <!-- Sidebar Toggle Button -->
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-chevron-left"></i>
            </button>

            <!-- Sidebar Brand -->
            <div class="sidebar-brand">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-building"></i>
                </div>
                @if (SignInManager.IsSignedIn(User) && User.IsInRole("Provider"))
                {
                    <a href="/Provider/Index" class="sidebar-brand-text" title="الانتقال الى لوحة المعلومات - Dashboard">نظام ERP</a>
                }
                else
                {
                    <a href="/Home/Index" class="sidebar-brand-text" title="الانتقال الى لوحة المعلومات - Dashboard">نظام ERP</a>
                }
            </div>

            <!-- Sidebar Navigation -->
            <nav class="sidebar-nav">
                <!-- Main Section -->
                <div class="nav-section">
                    <div class="nav-section-title">القائمة الرئيسية</div>
                    <ul class="nav-list">

                @if (SignInManager.IsSignedIn(User) && User.IsInRole("Admin"))
                {
                        <li class="nav-item">
                            <a href="/Home/Index" class="nav-link active">
                                <i class="nav-link-icon fas fa-home"></i>
                                <span class="nav-link-text">لوحة التحكم</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Management Section -->
                <div class="nav-section">
                    <div class="nav-section-title">الإدارة</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Employees" asp-action="Index">
                                <i class="nav-link-icon bi bi-people"></i>
                                <span class="nav-link-text">@localizer["theemployees"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Business" asp-action="Index">
                                <i class="nav-link-icon bi bi-briefcase"></i>
                                <span class="nav-link-text">@localizer["Business"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Events" asp-action="Index">
                                <i class="nav-link-icon fas fa-calendar-alt"></i>
                                <span class="nav-link-text">الفعاليات</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Activities" asp-action="Index">
                                <i class="nav-link-icon bi bi-check2-square"></i>
                                <span class="nav-link-text">@localizer["activities"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Tasks" asp-action="Index">
                                <i class="nav-link-icon bi bi-list-task"></i>
                                <span class="nav-link-text">@localizer["tasks"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Marketers" asp-action="Index">
                                <i class="nav-link-icon bi bi-graph-up"></i>
                                <span class="nav-link-text">@localizer["markters"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="ServiceProvider" asp-action="Index">
                                <i class="nav-link-icon bi bi-person-workspace"></i>
                                <span class="nav-link-text">@localizer["serviceproviders"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Investors" asp-action="Index">
                                <i class="nav-link-icon bi bi-piggy-bank"></i>
                                <span class="nav-link-text">@localizer["investmentround"]</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Financial Section -->
                <div class="nav-section">
                    <div class="nav-section-title">المالية</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="PaymentsRequests" asp-action="Index">
                                <i class="nav-link-icon fas fa-money-check-alt"></i>
                                <span class="nav-link-text">@localizer["PaymentsRequests"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Transcations" asp-action="Index">
                                <i class="nav-link-icon bi bi-cash-coin"></i>
                                <span class="nav-link-text">@localizer["fininacalTranscations"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="ReplacePoints" asp-action="Index1">
                                <i class="nav-link-icon fas fa-exchange-alt"></i>
                                <span class="nav-link-text">طلبات استبدال النقاط</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="GarentedDiscounts" asp-action="Report">
                                <i class="nav-link-icon fas fa-percentage"></i>
                                <span class="nav-link-text">@localizer["GrantedTranscations"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="BanksAccounts" asp-action="Index">
                                <i class="nav-link-icon bi bi-bank"></i>
                                <span class="nav-link-text">@localizer["bankaccount"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="PayingCompanies" asp-action="Index">
                                <i class="nav-link-icon bi bi-credit-card-2-front"></i>
                                <span class="nav-link-text">@localizer["payingcom"]</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Products & Services Section -->
                <div class="nav-section">
                    <div class="nav-section-title">المنتجات والخدمات</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="SpecialOffers" asp-action="Index">
                                <i class="nav-link-icon bi bi-gift"></i>
                                <span class="nav-link-text">@localizer["offers"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="UserPackages" asp-action="Index">
                                <i class="nav-link-icon bi bi-box-seam"></i>
                                <span class="nav-link-text">@localizer["packages"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Categories" asp-action="Index">
                                <i class="nav-link-icon bi bi-tags"></i>
                                <span class="nav-link-text">@localizer["cates"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Products" asp-action="Index">
                                <i class="nav-link-icon bi bi-box2"></i>
                                <span class="nav-link-text">@localizer["products"]</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Customers Section -->
                <div class="nav-section">
                    <div class="nav-section-title">العملاء</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="SystemUsers" asp-action="Index">
                                <i class="nav-link-icon bi bi-people-fill"></i>
                                <span class="nav-link-text">@localizer["ClientsCollections"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Subscriptions" asp-action="Index">
                                <i class="nav-link-icon bi bi-journal-check"></i>
                                <span class="nav-link-text">@localizer["subscriptions"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="GarentedDiscounts" asp-action="Index">
                                <i class="nav-link-icon bi bi-percent"></i>
                                <span class="nav-link-text">@localizer["Customersavingspoints"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Agents" asp-action="Index">
                                <i class="nav-link-icon bi bi-person-badge"></i>
                                <span class="nav-link-text">@localizer["customers"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Orders" asp-action="Index">
                                <i class="nav-link-icon bi bi-truck"></i>
                                <span class="nav-link-text">@localizer["orderstracking"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="CardOrder" asp-action="Index">
                                <i class="nav-link-icon bi bi-person-vcard"></i>
                                <span class="nav-link-text">@localizer["waitinglist"]</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Reports Section -->
                <div class="nav-section">
                    <div class="nav-section-title">التقارير</div>
                    <ul class="nav-list">
                        <li class="nav-item has-submenu">
                            <a href="#" class="nav-link">
                                <i class="nav-link-icon fas fa-chart-line"></i>
                                <span class="nav-link-text">@localizer["Reports"]</span>
                                <span class="badge badge-primary nav-link-badge">6</span>
                            </a>
                            <ul class="submenu">
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Reports" asp-action="SystemPerformance">
                                        <span class="nav-link-text">@localizer["overallperformance"]</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Reports" asp-action="TopCustomersReport">
                                        <span class="nav-link-text">@localizer["MostEngagedCustomers"]</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Reports" asp-action="TopProvidersReport">
                                        <span class="nav-link-text">@localizer["MostEngagedDistributors"]</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Reports" asp-action="SubscriptionsOverview">
                                        <span class="nav-link-text">@localizer["Recurringsubscriptions"]</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="Reports" asp-action="MonthlyGrowth">
                                        <span class="nav-link-text">@localizer["Monthlyrevenue"]</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" asp-controller="GarentedDiscounts" asp-action="Report">
                                        <span class="nav-link-text">@localizer["GrantingpointsReport"]</span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <!-- Settings Section -->
                <div class="nav-section">
                    <div class="nav-section-title">الإعدادات</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="CustomerRewardSettings" asp-action="Index">
                                <i class="nav-link-icon bi bi-stars"></i>
                                <span class="nav-link-text">@localizer["SettingsHalaCoinz"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Copun" asp-action="Index">
                                <i class="nav-link-icon bi bi-ticket-perforated"></i>
                                <span class="nav-link-text">@localizer["coupons"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Countries" asp-action="Index">
                                <i class="nav-link-icon bi bi-geo-alt"></i>
                                <span class="nav-link-text">@localizer["countriescities"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Teams" asp-action="Index">
                                <i class="nav-link-icon bi bi-people"></i>
                                <span class="nav-link-text">@(isRTL ? "الفريق" : "Team")</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Achievements" asp-action="Index">
                                <i class="nav-link-icon bi bi-trophy"></i>
                                <span class="nav-link-text">@(isRTL ? "الانجازات" : "Achievements")</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="WebSite" asp-action="Index">
                                <i class="nav-link-icon bi bi-globe"></i>
                                <span class="nav-link-text">@localizer["websitecontrol"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Message" asp-action="SendMessage">
                                <i class="nav-link-icon bi bi-bell"></i>
                                <span class="nav-link-text">اشعارات فايربيس</span>
                            </a>
                        </li>

                    </ul>
                </div>
                }
           
                @if (SignInManager.IsSignedIn(User) && User.IsInRole("Marketer"))
                {
                        <li class="nav-item">
                            <a class="nav-link active" asp-controller="MarketerUser" asp-action="Index">
                                <i class="nav-link-icon fas fa-home"></i>
                                <span class="nav-link-text">@localizer["home"]</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Service Providers Section -->
                <div class="nav-section">
                    <div class="nav-section-title">مقدمو الخدمات</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="MarketerUser" asp-action="CreateProvider">
                                <i class="nav-link-icon bi bi-person-plus"></i>
                                <span class="nav-link-text">@localizer["registerserviceprovider"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="MarketerUser" asp-action="Providers">
                                <i class="nav-link-icon bi bi-person-workspace"></i>
                                <span class="nav-link-text">@localizer["serviceproviders"]</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Customers & Finance Section -->
                <div class="nav-section">
                    <div class="nav-section-title">العملاء والمالية</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Marketers" asp-action="Customers">
                                <i class="nav-link-icon bi bi-people-fill"></i>
                                <span class="nav-link-text">@localizer["customerstracking"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="MarketerUser" asp-action="Finance">
                                <i class="nav-link-icon bi bi-cash-coin"></i>
                                <span class="nav-link-text">@localizer["financialtransactions"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="MarketerUser" asp-action="Tasks">
                                <i class="nav-link-icon bi bi-list-task"></i>
                                <span class="nav-link-text">@localizer["taskstracking"]</span>
                            </a>
                        </li>
                    </ul>
                </div>
                }
              
                @if (SignInManager.IsSignedIn(User) && User.IsInRole("Employee"))
                {
                        <li class="nav-item">
                            <a class="nav-link active" asp-controller="EmployeeData" asp-action="Index">
                                <i class="nav-link-icon fas fa-home"></i>
                                <span class="nav-link-text">@localizer["home"]</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Employee Tasks Section -->
                <div class="nav-section">
                    <div class="nav-section-title">المهام</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="EmployeeData" asp-action="Create">
                                <i class="nav-link-icon bi bi-plus-circle"></i>
                                <span class="nav-link-text">@localizer["addtasks"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="EmployeeData" asp-action="Tasks">
                                <i class="nav-link-icon bi bi-list-task"></i>
                                <span class="nav-link-text">@localizer["taskstracking"]</span>
                            </a>
                        </li>
                    </ul>
                </div>
                }
            
                @if (SignInManager.IsSignedIn(User) && User.IsInRole("Provider"))
                {
                        <li class="nav-item">
                            <a class="nav-link active" asp-controller="Provider" asp-action="Index">
                                <i class="nav-link-icon fas fa-home"></i>
                                <span class="nav-link-text">@localizer["home"]</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Agreements Section -->
                <div class="nav-section">
                    <div class="nav-section-title">الاتفاقيات</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="ServiceAgreement" asp-action="IndexProvider">
                                <i class="nav-link-icon fas fa-handshake"></i>
                                <span class="nav-link-text">اتفاقيات</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Account Management Section -->
                <div class="nav-section">
                    <div class="nav-section-title">إدارة الحساب</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="serviceProvider" asp-action="EditPersonalData">
                                <i class="nav-link-icon fas fa-user-edit"></i>
                                <span class="nav-link-text">@localizer["Editdata"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="serviceProvider" asp-action="ChangePassword">
                                <i class="nav-link-icon fas fa-key"></i>
                                <span class="nav-link-text">@localizer["Changepassword"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Employees" asp-action="Index">
                                <i class="nav-link-icon bi bi-people"></i>
                                <span class="nav-link-text">@localizer["addemp"]</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Discounts & Coupons Section -->
                <div class="nav-section">
                    <div class="nav-section-title">الخصومات والكوبونات</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="ProviderDiscount" asp-action="Index">
                                <i class="nav-link-icon fas fa-percentage"></i>
                                <span class="nav-link-text">@localizer["addcoupons"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Copun" asp-action="ProviderIndex">
                                <i class="nav-link-icon bi bi-ticket-perforated"></i>
                                <span class="nav-link-text">@localizer["coupons"]</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Financial Section -->
                <div class="nav-section">
                    <div class="nav-section-title">المالية</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="ProviderPoints" asp-action="Index">
                                <i class="nav-link-icon fas fa-coins"></i>
                                <span class="nav-link-text">@localizer["pointssettings"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="ChargeWalet" asp-action="Index">
                                <i class="nav-link-icon fas fa-wallet"></i>
                                <span class="nav-link-text">@localizer["chargewalet"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="ReplacePoints" asp-action="Index">
                                <i class="nav-link-icon fas fa-exchange-alt"></i>
                                <span class="nav-link-text">@localizer["pointsreplaceorders"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="ChargeWalet" asp-action="Index">
                                <i class="nav-link-icon fas fa-undo"></i>
                                <span class="nav-link-text">@localizer["returnremain"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="CustomDiscount" asp-action="DiscountsOrder">
                                <i class="nav-link-icon fas fa-tags"></i>
                                <span class="nav-link-text">@localizer["DiscountsOrder"]</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="CustomDiscount" asp-action="DiscountsOrder">
                                <i class="nav-link-icon fas fa-gift"></i>
                                <span class="nav-link-text">@localizer["grantdiscount"]</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Statistics Section -->
                <div class="nav-section">
                    <div class="nav-section-title">الإحصائيات</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Statistics" asp-action="Index">
                                <i class="nav-link-icon fas fa-chart-bar"></i>
                                <span class="nav-link-text">@localizer["statistics"]</span>
                            </a>
                        </li>
                    </ul>
                </div>
                }
                @if (SignInManager.IsSignedIn(User) && User.IsInRole("Customer"))
                {
                        <li class="nav-item">
                            <a class="nav-link active" href="/Home/Index">
                                <i class="nav-link-icon fas fa-home"></i>
                                <span class="nav-link-text">الرئيسية</span>
                            </a>
                        </li>
                    </ul>
                </div>
                }

                @if (SignInManager.IsSignedIn(User) && User.IsInRole("Business"))
                {
                        <li class="nav-item">
                            <a class="nav-link active" asp-controller="Business" asp-action="Index">
                                <i class="nav-link-icon fas fa-home"></i>
                                <span class="nav-link-text">@localizer["Business"]</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Business Management Section -->
                <div class="nav-section">
                    <div class="nav-section-title">إدارة الأعمال</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="BusinessEmp" asp-action="Index">
                                <i class="nav-link-icon bi bi-people"></i>
                                <span class="nav-link-text">الموظفين</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="ProviderDiscount" asp-action="Index">
                                <i class="nav-link-icon fas fa-percentage"></i>
                                <span class="nav-link-text">@localizer["addcoupons"]</span>
                            </a>
                        </li>
                    </ul>
                </div>
                }

            </nav>
        </aside>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>


        <!-- Main Content -->
        <div class="main-content" id="mainContent">
            <!-- Navbar -->
            <nav class="navbar" id="navbar">
                <!-- Navbar Left -->
                <div class="navbar-left">
                    <!-- Mobile Menu Toggle -->
                    <button class="navbar-menu-toggle" id="mobileMenuToggle" aria-expanded="false" aria-label="Toggle navigation">
                        <i class="fas fa-bars"></i>
                    </button>

                    <!-- Search Bar -->
                    <div class="navbar-search">
                        <input type="text" class="navbar-search-input" placeholder="البحث...">
                        <i class="fas fa-search navbar-search-icon"></i>
                    </div>
                </div>

                <!-- Navbar Right -->
                <div class="navbar-right">
                    <!-- Language Toggle -->
                    <partial name="_SelectLanguage" />

                    <!-- User Profile -->
                    <div class="navbar-dropdown navbar-user-dropdown">
                        <button class="navbar-dropdown-toggle">
                            <div class="navbar-user">
                                <div class="navbar-user-info">
                                    <div class="navbar-user-name">@User.Identity.Name</div>
                                    <div class="navbar-user-role">
                                        @if (User.IsInRole("Admin")) { <span>مدير النظام</span> }
                                        else if (User.IsInRole("Provider")) { <span>مقدم خدمة</span> }
                                        else if (User.IsInRole("Marketer")) { <span>مسوق</span> }
                                        else if (User.IsInRole("Employee")) { <span>موظف</span> }
                                        else if (User.IsInRole("Business")) { <span>أعمال</span> }
                                        else { <span>مستخدم</span> }
                                    </div>
                                </div>
                                <img src="/assets/img/default-avatar.png" alt="User Avatar" class="navbar-user-avatar">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </button>
                        <div class="navbar-dropdown-menu">
                            <a href="#" class="navbar-dropdown-item">
                                <div class="navbar-dropdown-item-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="navbar-dropdown-item-content">
                                    <div class="navbar-dropdown-item-title">الملف الشخصي</div>
                                </div>
                            </a>
                            <a href="#" class="navbar-dropdown-item">
                                <div class="navbar-dropdown-item-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="navbar-dropdown-item-content">
                                    <div class="navbar-dropdown-item-title">الإعدادات</div>
                                </div>
                            </a>
                            <div class="navbar-dropdown-footer">
                                <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
                                    <button type="submit" class="btn btn-link text-danger" style="text-decoration: none;">
                                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Content Wrapper -->
            <div class="content-wrapper" id="content-wrapper">
                @RenderBody()
            </div>
        </div>
    </div>
    @*</div>*@

    <footer class="footer pt-3  " style="bottom: 0; position: fixed; width: 100%;height: 71px; background: #fff;">
        <div class="container-fluid">
            <div class="row align-items-center justify-content-lg-between">
                <div class="col-lg-6 mb-lg-0 mb-4">
                    <div class="copyright text-center text-sm text-muted text-lg-start">

                        Made by
                        <a href="http://www.tkweentechno.com/">TAKWEEN</a>
                        For a better web.
                    </div>
                </div>
                @*   <div class="col-lg-6">
              <ul class="nav nav-footer justify-content-center justify-content-lg-end">
                <li class="nav-item">
                  <a href="https://www.creative-tim.com" class="nav-link text-muted" target="_blank">Creative Tim</a>
                </li>
                <li class="nav-item">
                  <a href="#" class="nav-link text-muted" target="_blank">About Us</a>
                </li>
                <li class="nav-item">
                  <a href="#" class="nav-link text-muted" target="_blank">Blog</a>
                </li>
                <li class="nav-item">
                  <a href="#" class="nav-link pe-0 text-muted" target="_blank">License</a>
                </li>
              </ul>
            </div> *@
            </div>
        </div>
    </footer>
    @* <script src="~/assets/js/core/popper.min.js"></script> *@
    <script src="~/assets/js/core/bootstrap.min.js"></script>
    @* <script src="~/assets/js/plugins/perfect-scrollbar.min.js"></script> *@
    @* <script src="~/assets/js/plugins/smooth-scrollbar.min.js"></script> *@
    @* <script src="~/assets/js/plugins/fullcalendar.min.js"></script> *@
    @* <script src="~/assets/js/plugins/chartjs.min.js"></script> *@
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/jquery-1.10.2.min.js"></script>
    <script src="~/js/jquery-ui.js"></script>
    @* <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script> *@

    @if (isRTL)
    {
        <script src="~/js/site.js" asp-append-version="true"></script>
    }
    else
    {
        <script src="~/js/ensite.js" asp-append-version="true"></script>
    }

    @* <script src="~/assets/js/plugins/choices.min.js"></script> *@
    @* <script src="https://polyfill.io/v3/polyfill.min.js?features=default"></script> *@



    @*<script src="~/js/map.js"></script>*@
    <!-- Github buttons -->
    @* <script async defer src="https://buttons.github.io/buttons.js"></script> *@
    <!-- Control Center for Soft Dashboard: parallax effects, scripts for the example pages etc -->
    <script src="~/assets/js/soft-ui-dashboard.min.js?v=1.0.3"></script>
    <script src="~/js/dataTables.min.js"></script>
    <script src="~/js/toastr.min.js"></script>

    <!-- Template JavaScript -->
    <script src="~/js/template-main.js" asp-append-version="true"></script>

    <!-- Sidebar Debug Script - يمكن حذفه بعد التأكد من عمل الـ sidebar -->
    <script src="~/js/sidebar-debug.js"></script>
    @* <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script> *@
    <script src="https://cdnjs.cloudflare.com/ajax/libs/selectize.js/0.12.6/js/standalone/selectize.min.js" integrity="sha256-+C0A5Ilqmu4QcSPxrlGpaZxJ04VjsRjKu+G82kl5UJk=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/localization/messages_ar.js"></script>

    <script>
        $(document).ready(function () {
            $('.customselect').selectize({
                sortField: 'text'
            });
        });

    </script>
    @await RenderSectionAsync("Scripts", required: false)
     @if (isRTL)
    {
        <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/localization/messages_ar.js"></script>
    }
</body>
</html>
