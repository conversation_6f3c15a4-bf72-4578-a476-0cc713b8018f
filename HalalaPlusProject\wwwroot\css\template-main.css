/* 
 * ERP Dashboard - Main Stylesheet
 * الملف الرئيسي للأنماط
 */

/* ==================== Global Styles ==================== */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-regular);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  direction: rtl;
  text-align: right;
  overflow-x: hidden;
}

/* ==================== Typography ==================== */

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: var(--font-bold);
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-base);
}

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
}

/* ==================== Utility Classes ==================== */

.text-primary {
  color: var(--primary-color) !important;
}
.text-secondary {
  color: var(--text-secondary) !important;
}
.text-muted {
  color: var(--text-muted) !important;
}
.text-success {
  color: var(--success-color) !important;
}
.text-danger {
  color: var(--danger-color) !important;
}
.text-warning {
  color: var(--warning-color) !important;
}
.text-info {
  color: var(--info-color) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}
.bg-secondary {
  background-color: var(--bg-secondary) !important;
}
.bg-success {
  background-color: var(--success-color) !important;
}
.bg-danger {
  background-color: var(--danger-color) !important;
}
.bg-warning {
  background-color: var(--warning-color) !important;
}
.bg-info {
  background-color: var(--info-color) !important;
}

.fw-light {
  font-weight: var(--font-light) !important;
}
.fw-regular {
  font-weight: var(--font-regular) !important;
}
.fw-medium {
  font-weight: var(--font-medium) !important;
}
.fw-semibold {
  font-weight: var(--font-semibold) !important;
}
.fw-bold {
  font-weight: var(--font-bold) !important;
}

.rounded-sm {
  border-radius: var(--radius-sm) !important;
}
.rounded-md {
  border-radius: var(--radius-md) !important;
}
.rounded-lg {
  border-radius: var(--radius-lg) !important;
}
.rounded-xl {
  border-radius: var(--radius-xl) !important;
}

.shadow-sm {
  box-shadow: var(--shadow-sm) !important;
}
.shadow-md {
  box-shadow: var(--shadow-md) !important;
}
.shadow-lg {
  box-shadow: var(--shadow-lg) !important;
}

/* ==================== Layout ==================== */

.wrapper {
  display: flex;
  min-height: 100vh;
  width: 100%;
}

.main-content {
  flex: 1;
  margin-right: var(--sidebar-width);
  transition: margin-right var(--transition-base);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content.sidebar-collapsed {
  margin-right: var(--sidebar-collapsed-width);
}

.content-wrapper {
  flex: 1;
  padding: var(--spacing-xl);
  margin-top: var(--navbar-height);
}

/* ==================== Cards ==================== */

.card {
  background: var(--card-bg);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  transition: var(--transition-base);
  border: 1px solid var(--border-color);
}

.card:hover {
  box-shadow: var(--card-shadow-hover);
}

.card-header {
  padding-bottom: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-semibold);
  margin-bottom: 0;
  color: var(--text-primary);
}

.card-body {
  padding: 0;
}

.card-footer {
  padding-top: var(--spacing-md);
  margin-top: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

/* ==================== Buttons ==================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: 0.625rem 1.25rem;
  font-size: var(--font-size-base);
  font-weight: var(--font-medium);
  line-height: 1.5;
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: var(--transition-fast);
  text-decoration: none;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  color: var(--text-white);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--text-white);
}

.btn-success {
  background-color: var(--success-color);
  color: var(--text-white);
}

.btn-danger {
  background-color: var(--danger-color);
  color: var(--text-white);
}

.btn-warning {
  background-color: var(--warning-color);
  color: var(--text-white);
}

.btn-outline-primary {
  background-color: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-sm {
  padding: 0.375rem 0.875rem;
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: 0.875rem 1.75rem;
  font-size: var(--font-size-lg);
}

/* ==================== Forms ==================== */

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.625rem 1rem;
  font-size: var(--font-size-base);
  font-weight: var(--font-regular);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control::placeholder {
  color: var(--text-muted);
}

textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  padding-right: 2.5rem;
  padding-left: 1rem;
}

/* RTL form select */
[dir="rtl"] .form-select {
  background-position: right 0.75rem center;
  padding-right: 2.5rem;
  padding-left: 1rem;
}

/* LTR form select */
[dir="ltr"] .form-select {
  background-position: left 0.75rem center;
  padding-left: 2.5rem;
  padding-right: 1rem;
}

/* ==================== Tables ==================== */

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.table {
  width: 100%;
  margin-bottom: var(--spacing-lg);
  border-collapse: collapse;
  min-width: 600px; /* Minimum width for proper display */
}

.table th,
.table td {
  padding: var(--spacing-md);
  text-align: right;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
  white-space: nowrap;
}

.table thead th {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
  border-bottom: 2px solid var(--border-dark);
  position: sticky;
  top: 0;
  z-index: 10;
}

.table tbody tr {
  transition: var(--transition-fast);
}

.table tbody tr:hover {
  background-color: var(--bg-secondary);
  transform: scale(1.01);
}

/* Table text alignment for different directions */
[dir="rtl"] .table th,
[dir="rtl"] .table td {
  text-align: right;
}

[dir="ltr"] .table th,
[dir="ltr"] .table td {
  text-align: left;
}

/* ==================== Badges ==================== */

.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  font-size: var(--font-size-xs);
  font-weight: var(--font-semibold);
  line-height: 1;
  border-radius: var(--radius-full);
}

.badge-primary {
  background-color: var(--primary-color);
  color: white;
}
.badge-success {
  background-color: var(--success-color);
  color: white;
}
.badge-danger {
  background-color: var(--danger-color);
  color: white;
}
.badge-warning {
  background-color: var(--warning-color);
  color: white;
}
.badge-info {
  background-color: var(--info-color);
  color: white;
}
.badge-secondary {
  background-color: var(--secondary-color);
  color: white;
}

/* ==================== Responsive ==================== */

/* Tablet and below */
@media (max-width: 991.98px) {
  .main-content {
    margin-right: 0 !important;
  }

  .main-content.sidebar-collapsed {
    margin-right: 0 !important;
  }

  .content-wrapper {
    padding: var(--spacing-md);
    margin-top: var(--navbar-height);
  }

  /* Improve button spacing on tablets */
  .btn {
    padding: 0.5rem 1rem;
    font-size: var(--font-size-sm);
  }

  .btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: var(--font-size-xs);
  }
}

/* Mobile phones */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }

  .content-wrapper {
    padding: 12px !important;
    margin-top: 64px !important; /* Fixed navbar height */
  }

  /* Ensure consistent layout between navbar and sidebar */
  .main-content {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  /* Typography adjustments for mobile */
  h1 {
    font-size: var(--font-size-2xl);
  }
  h2 {
    font-size: var(--font-size-xl);
  }
  h3 {
    font-size: var(--font-size-lg);
  }
  h4 {
    font-size: var(--font-size-base);
  }
  h5 {
    font-size: var(--font-size-sm);
  }
  h6 {
    font-size: var(--font-size-xs);
  }

  /* Button improvements for mobile */
  .btn {
    padding: 0.75rem 1.25rem;
    font-size: var(--font-size-base);
    min-height: 44px; /* Touch-friendly minimum height */
  }

  .btn-sm {
    padding: 0.5rem 1rem;
    font-size: var(--font-size-sm);
    min-height: 36px;
  }

  .btn-lg {
    padding: 1rem 2rem;
    font-size: var(--font-size-lg);
    min-height: 52px;
  }

  /* Form improvements for mobile */
  .form-control {
    padding: 0.75rem 1rem;
    font-size: var(--font-size-base);
    min-height: 44px;
  }

  .form-select {
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    min-height: 44px;
  }

  /* Table improvements for mobile */
  .table {
    font-size: var(--font-size-sm);
  }

  .table th,
  .table td {
    padding: var(--spacing-sm);
  }
}

/* Small mobile phones */
@media (max-width: 575.98px) {
  html {
    font-size: 13px;
  }

  .content-wrapper {
    padding: var(--spacing-xs);
  }

  /* Further reduce spacing for very small screens */
  .card {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }

  /* Stack buttons vertically on very small screens */
  .btn-group-vertical .btn {
    width: 100%;
    margin-bottom: var(--spacing-xs);
  }
}
