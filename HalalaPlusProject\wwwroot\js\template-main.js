/**
 * ERP Dashboard - Main JavaScript
 * الملف الرئيسي للجافاسكريبت
 */

(function ($) {
  "use strict";

  // ==================== Global Variables ====================
  const sidebar = $("#sidebar");
  const mainContent = $("#mainContent");
  const navbar = $("#navbar");
  const contentWrapper = $("#content-wrapper");
  const sidebarToggle = $("#sidebarToggle");
  const mobileMenuToggle = $("#mobileMenuToggle");
  const sidebarOverlay = $("#sidebarOverlay");
  const languageToggle = $("#languageToggle");
  const languageDropdown = $("#languageDropdown");

  // ==================== Sidebar Toggle ====================
  function toggleSidebar() {
    sidebar.toggleClass("collapsed");
    mainContent.toggleClass("sidebar-collapsed");
    navbar.toggleClass("sidebar-collapsed");
    contentWrapper.toggleClass("sidebar-collapsed");

    // Save state to localStorage
    const isCollapsed = sidebar.hasClass("collapsed");
    localStorage.setItem("sidebarCollapsed", isCollapsed);
  }

  // Restore sidebar state from localStorage
  function restoreSidebarState() {
    const isCollapsed = localStorage.getItem("sidebarCollapsed") === "true";
    if (isCollapsed) {
      sidebar.addClass("collapsed");
      mainContent.addClass("sidebar-collapsed");
      navbar.addClass("sidebar-collapsed");
      contentWrapper.addClass("sidebar-collapsed");
    }
  }

  // Desktop sidebar toggle
  sidebarToggle.on("click", function (e) {
    e.preventDefault();
    toggleSidebar();
  });

  // Mobile menu toggle
  mobileMenuToggle.on("click", function (e) {
    e.preventDefault();
    const isOpen = sidebar.hasClass("show");

    sidebar.toggleClass("show");
    sidebarOverlay.toggleClass("show");
    $("body").toggleClass("sidebar-open");

    // Update aria-expanded for accessibility
    mobileMenuToggle.attr("aria-expanded", !isOpen);

    // Focus management for accessibility
    if (!isOpen) {
      // When opening, focus on the first navigation link
      setTimeout(() => {
        sidebar.find(".nav-link").first().focus();
      }, 300);
    }
  });

  // Close sidebar on overlay click (mobile)
  sidebarOverlay.on("click", function () {
    sidebar.removeClass("show");
    sidebarOverlay.removeClass("show");
    $("body").removeClass("sidebar-open");

    // Update aria-expanded for accessibility
    mobileMenuToggle.attr("aria-expanded", "false");

    // Return focus to menu toggle button
    mobileMenuToggle.focus();
  });

  // Close sidebar on Escape key
  $(document).on("keydown", function(e) {
    if (e.key === "Escape" && sidebar.hasClass("show")) {
      sidebar.removeClass("show");
      sidebarOverlay.removeClass("show");
      $("body").removeClass("sidebar-open");
      mobileMenuToggle.attr("aria-expanded", "false");
      mobileMenuToggle.focus();
    }
  });

  // ==================== Submenu Toggle ====================
  $(".nav-item.has-submenu > .nav-link").on("click", function (e) {
    e.preventDefault();
    const navItem = $(this).parent();
    const isOpen = navItem.hasClass("open");

    // Close all other submenus
    $(".nav-item.has-submenu").not(navItem).removeClass("open");

    // Toggle current submenu
    navItem.toggleClass("open");
  });

  // ==================== Dropdown Toggle ====================
  $(".navbar-dropdown-toggle, .navbar-action").on("click", function (e) {
    e.preventDefault();
    e.stopPropagation();

    const dropdown = $(this).closest(".navbar-dropdown");
    const isOpen = dropdown.hasClass("show");

    // Close all dropdowns
    $(".navbar-dropdown").removeClass("show");

    // Toggle current dropdown
    if (!isOpen) {
      dropdown.addClass("show");
    }
  });

  // Close dropdowns when clicking outside
  $(document).on("click", function (e) {
    if (!$(e.target).closest(".navbar-dropdown").length) {
      $(".navbar-dropdown").removeClass("show");
    }
  });

  // Prevent dropdown from closing when clicking inside
  $(".navbar-dropdown-menu").on("click", function (e) {
    e.stopPropagation();
  });

  // ==================== Toastr Configuration ====================
  if (typeof toastr !== "undefined") {
    toastr.options = {
      closeButton: true,
      debug: false,
      newestOnTop: true,
      progressBar: true,
      positionClass: "toast-top-left",
      preventDuplicates: false,
      onclick: null,
      showDuration: "300",
      hideDuration: "1000",
      timeOut: "5000",
      extendedTimeOut: "1000",
      showEasing: "swing",
      hideEasing: "linear",
      showMethod: "fadeIn",
      hideMethod: "fadeOut",
      rtl: true,
    };
  }

  // ==================== SweetAlert2 Configuration ====================
  if (typeof Swal !== "undefined") {
    // Set default options for SweetAlert2
    const swalWithBootstrap = Swal.mixin({
      customClass: {
        confirmButton: "btn btn-primary me-2",
        cancelButton: "btn btn-secondary",
      },
      buttonsStyling: false,
      reverseButtons: true,
    });

    // Make it globally available
    window.swalWithBootstrap = swalWithBootstrap;
  }

  // ==================== Utility Functions ====================

  /**
   * Show success notification
   */
  window.showSuccess = function (message, title = "نجح!") {
    if (typeof toastr !== "undefined") {
      toastr.success(message, title);
    }
  };

  /**
   * Show error notification
   */
  window.showError = function (message, title = "خطأ!") {
    if (typeof toastr !== "undefined") {
      toastr.error(message, title);
    }
  };

  /**
   * Show warning notification
   */
  window.showWarning = function (message, title = "تحذير!") {
    if (typeof toastr !== "undefined") {
      toastr.warning(message, title);
    }
  };

  /**
   * Show info notification
   */
  window.showInfo = function (message, title = "معلومة") {
    if (typeof toastr !== "undefined") {
      toastr.info(message, title);
    }
  };

  /**
   * Confirm dialog
   */
  window.confirmDialog = function (options) {
    if (typeof Swal !== "undefined") {
      return Swal.fire({
        title: options.title || "هل أنت متأكد؟",
        text: options.text || "لن تتمكن من التراجع عن هذا الإجراء!",
        icon: options.icon || "warning",
        showCancelButton: true,
        confirmButtonText: options.confirmText || "نعم، متأكد",
        cancelButtonText: options.cancelText || "إلغاء",
        reverseButtons: true,
      });
    }
    return Promise.resolve({ isConfirmed: confirm(options.text) });
  };

  /**
   * Format number with thousands separator
   */
  window.formatNumber = function (number) {
    return new Intl.NumberFormat("ar-SA").format(number);
  };

  /**
   * Format currency
   */
  window.formatCurrency = function (amount, currency = "ر.س") {
    return formatNumber(amount) + " " + currency;
  };

  /**
   * Format date
   */
  window.formatDate = function (date, format = "short") {
    const d = new Date(date);
    const options =
      format === "short"
        ? { year: "numeric", month: "2-digit", day: "2-digit" }
        : { year: "numeric", month: "long", day: "numeric" };
    return new Intl.DateTimeFormat("ar-SA", options).format(d);
  };

  /**
   * Debounce function
   */
  window.debounce = function (func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  // ==================== Search Functionality ====================
  const searchInput = $(".navbar-search-input");
  if (searchInput.length) {
    searchInput.on(
      "keyup",
      debounce(function () {
        const query = $(this).val().trim();
        if (query.length >= 3) {
          // Implement search functionality here
          console.log("Searching for:", query);
        }
      }, 300)
    );
  }

  // ==================== Responsive Handling ====================
  function handleResize() {
    const width = $(window).width();

    // Close mobile sidebar on resize to desktop
    if (width >= 992) {
      sidebar.removeClass("show");
      sidebarOverlay.removeClass("show");
      $("body").removeClass("sidebar-open");
      mobileMenuToggle.attr("aria-expanded", "false");
    }

    // Ensure navbar and sidebar consistency at different breakpoints
    if (width <= 767.98) {
      // Ensure proper alignment at mobile breakpoint
      ensureMobileAlignment();
    }
  }

  // Ensure proper alignment between navbar and sidebar on mobile with FIXED VALUES
  function ensureMobileAlignment() {
    const width = $(window).width();

    if (width <= 768) {
      // Force fixed heights for mobile
      navbar.css({
        'height': '64px',
        'min-height': '64px',
        'max-height': '64px'
      });

      sidebar.find('.sidebar-brand').css({
        'height': '64px',
        'min-height': '64px',
        'max-height': '64px'
      });

      // Ensure menu toggle button is properly sized
      mobileMenuToggle.css({
        'width': '44px',
        'height': '44px'
      });

      // Ensure navbar actions are properly sized
      $('.navbar-action').css({
        'width': '44px',
        'height': '44px'
      });
    }
  }

  $(window).on("resize", debounce(handleResize, 250));

  // ==================== Smooth Scroll ====================
  $('a[href^="#"]').on("click", function (e) {
    const target = $(this.getAttribute("href"));
    if (target.length) {
      e.preventDefault();
      $("html, body")
        .stop()
        .animate(
          {
            scrollTop: target.offset().top - 100,
          },
          500
        );
    }
  });

  // ==================== Initialize ====================
  function init() {
    // Restore sidebar state
    restoreSidebarState();

    // Show welcome notification
    setTimeout(function () {
      if (typeof toastr !== "undefined") {
        showInfo("مرحباً بك في نظام إدارة موارد المؤسسة", "مرحباً!");
      }
    }, 1000);
  }

  // ==================== Document Ready ====================
  $(document).ready(function () {
    init();

    // Ensure proper alignment after page load
    setTimeout(function() {
      if ($(window).width() <= 767.98) {
        ensureMobileAlignment();
      }
    }, 100);
  });
})(jQuery);
